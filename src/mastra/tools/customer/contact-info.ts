import { createTool } from "@mastra/core/tools";
import { z } from "zod";

/**
 * Công cụ gửi hình ảnh cho khách hàng
 * Sử dụng Supabase để gửi hình ảnh cho khách hàng
 */
export const getContactInfo = createTool({
  id: "get_contact_info",
  description: "Thu thập thông tin liên hệ để nhân viên tư vấn liên hệ hỗ trợ",
  inputSchema: z.object({
    phone: z.string().describe("Số điện thoại liên hệ"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");
      // console.log(runtimeContext)

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Gửi hình ảnh cho khách hàng
    //   const result = await sendImages({
    //     images: context.images,
    //     tenant_id: tenant_id.toString(),
    //   });
    console.log('Đã gửi: ', context.phone);

    //   if (!result.success) {
    //     console.error(`Không thể gửi hình ảnh: ${result.message}`);
    //     return {
    //       success: false,
    //       error: result.message,
    //     };
    //   }

      return {
        success: true,
        // data: result.data,
        message: "Đã gửi hình ảnh thành công cho người dùng",
      };
    } catch (error: any) {
      console.error("Lỗi khi gửi hình ảnh:", error);
      return {
        success: false,
        error: `Lỗi khi gửi hình ảnh: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
